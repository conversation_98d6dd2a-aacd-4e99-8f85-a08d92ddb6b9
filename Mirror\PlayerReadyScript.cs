using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using Mirror;

public class PlayerReadyScript : NetworkBehaviour
{
    public Text statusText;
    public Button readyButton;
    public Text playerCountText;
    public Text readyCountText;
    public Image player1ReadyImage; // تصویر نشان‌دهنده آمادگی بازیکن اول
    public Image player2ReadyImage; // تصویر نشان‌دهنده آمادگی بازیکن دوم

    private bool isReady = false;
    [SyncVar] private int readyCount = 0;

    void Start()
    {
        // غیرفعال کردن دکمه و تنظیم رنگ پیش‌فرض تصاویر
        readyButton.interactable = false;
        readyButton.onClick.AddListener(OnReadyClicked);

        // تنظیم رنگ قرمز برای تصاویر به‌صورت پیش‌فرض
        player1ReadyImage.color = Color.red;
        player2ReadyImage.color = Color.red;

        if (isServer)
        {
            StartCoroutine(CheckPlayers());
        }
    }

    public override void OnStartClient()
    {
        base.OnStartClient();
        ResetUI();
        CmdRequestPlayerCount();
    }

    public override void OnStopClient()
    {
        base.OnStopClient();
        ResetUI();
    }

    [Server]
    IEnumerator CheckPlayers()
    {
        while (true)
        {
            GameObject[] players = GameObject.FindGameObjectsWithTag("PlayCar");
            RpcUpdatePlayerCount(players.Length);

            if (players.Length >= 2)
            {
                RpcEnableReadyButton();
            }
            else
            {
                RpcDisableReadyButton();
            }
            yield return new WaitForSeconds(1f);
        }
    }

    [ClientRpc]
    void RpcUpdatePlayerCount(int count)
    {
        playerCountText.text = "Player Count: " + count;
        if (count < 2)
        {
            statusText.text = "Waiting for players to join the match...";
        }
    }

    [ClientRpc]
    void RpcEnableReadyButton()
    {
        if (!isReady)
        {
            readyButton.interactable = true;
            statusText.text = "Enough players joined. Press the Ready button";
        }
    }

    [ClientRpc]
    void RpcDisableReadyButton()
    {
        readyButton.interactable = false;
    }

    public void OnReadyClicked()
    {
        if (!isReady)
        {
            isReady = true;
            readyButton.interactable = false;
            CmdPlayerReady();
        }
    }

    [Command(requiresAuthority = false)]
    void CmdPlayerReady()
    {
        readyCount++;
        RpcUpdateReadyStatus(readyCount);
        if (readyCount >= 2)
        {
            StartCoroutine(StartCountdown());
        }
    }

    [ClientRpc]
    void RpcUpdateReadyStatus(int count)
    {
        readyCountText.text = "Ready Count: " + count;
        statusText.text = $"{count} players are ready";

        // تغییر رنگ تصاویر بر اساس تعداد بازیکنان آماده
        if (count >= 1)
        {
            player1ReadyImage.color = Color.green; // بازیکن اول آماده
        }
        if (count >= 2)
        {
            player2ReadyImage.color = Color.green; // بازیکن دوم آماده
        }
    }

    [Server]
    IEnumerator StartCountdown()
    {
        RpcSetStatusText("All players are ready!");
        yield return new WaitForSeconds(1f);

        for (int i = 3; i > 0; i--)
        {
            RpcSetStatusText(i.ToString());
            yield return new WaitForSeconds(1f);
        }
        RpcSetStatusText("GO!");
        RpcEnableCarControl();
        RpcHideUI();
    }

    [ClientRpc]
    void RpcSetStatusText(string text)
    {
        statusText.text = text;
    }

    [ClientRpc]
    void RpcEnableCarControl()
    {
        GameObject[] players = GameObject.FindGameObjectsWithTag("PlayCar");
        foreach (GameObject player in players)
        {
            RCCP_CarController carController = player.GetComponent<RCCP_CarController>();
            if (carController != null)
            {
                carController.enabled = true;
            }
        }
    }

    [ClientRpc]
    void RpcHideUI()
    {
        // غیرفعال کردن همه چیز به جز playerCountText
        statusText.gameObject.SetActive(false);
        readyButton.gameObject.SetActive(false);
        readyCountText.gameObject.SetActive(false);
        player1ReadyImage.gameObject.SetActive(false);
        player2ReadyImage.gameObject.SetActive(false);
        // playerCountText فعال باقی می‌ماند
    }

    void ResetUI()
    {
        isReady = false;
        readyButton.interactable = false;
        readyCountText.text = "Ready Count: 0";
        statusText.text = "Waiting for players to join the match...";
        player1ReadyImage.color = Color.red;
        player2ReadyImage.color = Color.red;
    }

    [Command]
    void CmdRequestPlayerCount()
    {
        GameObject[] players = GameObject.FindGameObjectsWithTag("PlayCar");
        RpcUpdatePlayerCount(players.Length);
        if (players.Length >= 2)
        {
            RpcEnableReadyButton();
        }
        else
        {
            RpcDisableReadyButton();
        }
    }
}